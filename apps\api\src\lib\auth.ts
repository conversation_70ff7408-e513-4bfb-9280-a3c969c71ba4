import { Request, Response, NextFunction } from 'express';
// import { getAuth } from '@clerk/express';
import { UserService } from './userService';

export interface AuthenticatedRequest extends Request {
  userId?: string;
  clerkUserId?: string;
  user?: {
    id: string;
    clerkUserId: string;
    name: string;
    email: string;
    role: string;
  };
}

// Middleware to require authentication (temporarily disabled for setup)
export const requireAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  // Temporarily bypass authentication for setup
  console.log('Authentication temporarily disabled for setup');
  next();
};

// Middleware to optionally get auth info (temporarily disabled for setup)
export const optionalAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  // Temporarily bypass authentication for setup
  console.log('Optional authentication temporarily disabled for setup');
  next();
};

// Middleware to require manager role
export const requireManager = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (!req.user || req.user.role !== 'manager') {
    return res.status(403).json({ error: 'Manager role required' });
  }

  next();
};

import { Request, Response, NextFunction } from 'express';
import { getAuth } from '@clerk/express';
import { UserService } from './userService';

export interface AuthenticatedRequest extends Request {
  userId?: string;
  clerkUserId?: string;
  user?: {
    id: string;
    clerkUserId: string;
    name: string;
    email: string;
    role: string;
  };
}

// Middleware to require authentication
export const requireAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { userId } = getAuth(req);

    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Get user from our database using Clerk ID
    const user = await UserService.findByClerkId(userId);

    if (!user) {
      // User exists in Clerk but not in our database - try to create them
      console.log(`User ${userId} not found in database, attempting to create via getOrCreateUser`);
      const newUser = await UserService.getOrCreateUser(userId);
      req.user = newUser;
      req.clerkUserId = userId;
      req.userId = newUser.id;
    } else {
      req.user = user;
      req.clerkUserId = userId;
      req.userId = user.id;
    }

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
};

// Middleware to optionally get auth info
export const optionalAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { userId } = getAuth(req);

    if (userId) {
      const user = await UserService.findByClerkId(userId);
      if (user) {
        req.user = user;
        req.clerkUserId = userId;
        req.userId = user.id;
      }
    }

    next();
  } catch (error) {
    console.error('Optional authentication error:', error);
    // Don't fail for optional auth, just continue without user
    next();
  }
};

// Middleware to require manager role
export const requireManager = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (!req.user || req.user.role !== 'manager') {
    return res.status(403).json({ error: 'Manager role required' });
  }

  next();
};

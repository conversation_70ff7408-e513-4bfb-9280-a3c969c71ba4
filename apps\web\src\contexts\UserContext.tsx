import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useUser } from '@clerk/clerk-react';

interface User {
  id: string;
  clerkUserId: string;
  name: string;
  email: string;
  role: 'sales' | 'designer' | 'supervisor' | 'manager';
}

interface UserContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  refreshUser: () => Promise<void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const useUserContext = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUserContext must be used within a UserProvider');
  }
  return context;
};

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider = ({ children }: UserProviderProps) => {
  const { user: clerkUser, isLoaded: clerkLoaded } = useUser();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserData = async (clerkUserId: string) => {
    try {
      console.log('Fetching user data for Clerk ID:', clerkUserId);

      const response = await fetch('/api/users/me', {
        credentials: 'include', // Include cookies for Clerk session
      });

      console.log('API response status:', response.status);

      if (response.ok) {
        const userData = await response.json();
        console.log('User data received:', userData);
        setUser(userData);
        setError(null);
      } else if (response.status === 401) {
        // User not authenticated - this is normal for signed-out users
        console.log('User not authenticated (401)');
        setUser(null);
        setError(null);
      } else if (response.status === 403) {
        // User authenticated but access denied
        console.log('Access denied (403)');
        setUser(null);
        setError('Access denied. Please contact an administrator.');
      } else if (response.status >= 500) {
        // Server error
        console.log('Server error:', response.status);
        throw new Error('Server error. Please try again later.');
      } else {
        // Other client errors
        const errorData = await response.json().catch(() => ({}));
        console.log('Client error:', response.status, errorData);
        throw new Error(errorData.error || `Request failed with status ${response.status}`);
      }
    } catch (err) {
      console.error('Error fetching user data:', err);

      if (err instanceof TypeError && err.message.includes('fetch')) {
        // Network error
        setError('Network error. Please check your connection and try again.');
      } else {
        setError(err instanceof Error ? err.message : 'Failed to fetch user data');
      }
      setUser(null);
    }
  };

  const refreshUser = async () => {
    if (clerkUser) {
      setLoading(true);
      setError(null);
      await fetchUserData(clerkUser.id);
      setLoading(false);
    }
  };

  // Retry mechanism for failed requests
  const retryFetchUser = async (retries = 3) => {
    for (let i = 0; i < retries; i++) {
      try {
        if (clerkUser) {
          await fetchUserData(clerkUser.id);
          return; // Success, exit retry loop
        }
      } catch (err) {
        if (i === retries - 1) {
          // Last retry failed
          console.error('All retry attempts failed:', err);
        } else {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
      }
    }
  };

  useEffect(() => {
    if (clerkLoaded) {
      if (clerkUser) {
        fetchUserData(clerkUser.id).finally(() => setLoading(false));
      } else {
        // User not signed in
        setUser(null);
        setError(null);
        setLoading(false);
      }
    }
  }, [clerkUser, clerkLoaded]);

  const value: UserContextType = {
    user,
    loading: loading || !clerkLoaded,
    error,
    refreshUser,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

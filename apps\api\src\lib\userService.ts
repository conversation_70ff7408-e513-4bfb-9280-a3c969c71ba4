import { PrismaClient } from '@prisma/client';
// import { clerk<PERSON>lient } from '@clerk/express';

const prisma = new PrismaClient();

export interface CreateUserData {
  clerkUserId: string;
  email: string;
  name: string;
  role?: string;
}

export interface UpdateUserData {
  email?: string;
  name?: string;
  role?: string;
}

export class UserService {
  // Create a new user in our database
  static async createUser(data: CreateUserData) {
    const { clerkUserId, email, name, role = 'sales' } = data;
    
    return await prisma.user.create({
      data: {
        id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        clerkUserId,
        email,
        name,
        role,
      },
    });
  }

  // Find user by Clerk ID
  static async findByClerkId(clerkUserId: string) {
    return await prisma.user.findUnique({
      where: { clerkUserId },
    });
  }

  // Find user by internal ID
  static async findById(id: string) {
    return await prisma.user.findUnique({
      where: { id },
    });
  }

  // Update user
  static async updateUser(clerkUserId: string, data: UpdateUserData) {
    return await prisma.user.update({
      where: { clerkUserId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
  }

  // Delete user
  static async deleteUser(clerkUserId: string) {
    return await prisma.user.delete({
      where: { clerkUserId },
    });
  }

  // Get or create user (temporarily simplified for setup)
  static async getOrCreateUser(clerkUserId: string) {
    // First try to find existing user
    let user = await this.findByClerkId(clerkUserId);

    if (user) {
      return user;
    }

    // Temporarily create a dummy user for setup
    console.log('Creating temporary user for setup');
    user = await this.createUser({
      clerkUserId,
      email: '<EMAIL>',
      name: 'Temporary User',
      role: 'sales',
    });

    return user;
  }

  // Update user role (only for managers)
  static async updateUserRole(clerkUserId: string, newRole: string, updatedByUserId: string) {
    // Check if the user making the change is a manager
    const updatedByUser = await this.findByClerkId(updatedByUserId);
    
    if (!updatedByUser || updatedByUser.role !== 'manager') {
      throw new Error('Only managers can update user roles');
    }

    return await this.updateUser(clerkUserId, { role: newRole });
  }

  // Get all users (for managers)
  static async getAllUsers() {
    return await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
    });
  }

  // Sync user data from Clerk (temporarily disabled for setup)
  static async syncUserFromClerk(clerkUserId: string) {
    console.log('Clerk sync temporarily disabled for setup');
    throw new Error('Clerk sync temporarily disabled for setup');
  }
}

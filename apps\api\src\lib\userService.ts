import { PrismaClient } from '@prisma/client';
// import { clerk<PERSON>lient } from '@clerk/express';

const prisma = new PrismaClient();

export interface CreateUserData {
  clerkUserId: string;
  email: string;
  name: string;
  role?: string;
}

export interface UpdateUserData {
  email?: string;
  name?: string;
  role?: string;
}

export class UserService {
  // Create a new user in our database
  static async createUser(data: CreateUserData) {
    const { clerkUserId, email, name, role = 'sales' } = data;
    
    return await prisma.user.create({
      data: {
        id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        clerkUserId,
        email,
        name,
        role,
      },
    });
  }

  // Find user by Clerk ID
  static async findByClerkId(clerkUserId: string) {
    return await prisma.user.findUnique({
      where: { clerkUserId },
    });
  }

  // Find user by internal ID
  static async findById(id: string) {
    return await prisma.user.findUnique({
      where: { id },
    });
  }

  // Update user
  static async updateUser(clerkUserId: string, data: UpdateUserData) {
    return await prisma.user.update({
      where: { clerkUserId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
  }

  // Delete user
  static async deleteUser(clerkUserId: string) {
    return await prisma.user.delete({
      where: { clerkUserId },
    });
  }

  // Get or create user - fetches from Clerk if needed
  static async getOrCreateUser(clerkUserId: string) {
    // First try to find existing user
    let user = await this.findByClerkId(clerkUserId);

    if (user) {
      return user;
    }

    // User not found in our database, but exists in Clerk
    // This can happen if webhook failed or user was created before webhook was set up
    console.log(`User ${clerkUserId} not found in database, creating with default values`);

    // For now, create with default values - the webhook should update this with real data
    // In a production setup, you might want to fetch user data from Clerk API here
    user = await this.createUser({
      clerkUserId,
      email: `user-${clerkUserId}@temp.example.com`, // Temporary email
      name: 'New User', // Temporary name
      role: 'sales', // Default role
    });

    console.log(`Created temporary user record for ${clerkUserId}. Webhook should update with real data.`);
    return user;
  }

  // Update user role (only for managers)
  static async updateUserRole(clerkUserId: string, newRole: string, updatedByUserId: string) {
    // Check if the user making the change is a manager
    const updatedByUser = await this.findByClerkId(updatedByUserId);
    
    if (!updatedByUser || updatedByUser.role !== 'manager') {
      throw new Error('Only managers can update user roles');
    }

    return await this.updateUser(clerkUserId, { role: newRole });
  }

  // Get all users (for managers)
  static async getAllUsers() {
    return await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
    });
  }

  // Sync user data from Clerk (temporarily disabled for setup)
  static async syncUserFromClerk(clerkUserId: string) {
    console.log('Clerk sync temporarily disabled for setup');
    throw new Error('Clerk sync temporarily disabled for setup');
  }
}
